TASK 25-28 MEI 2025


1. <PERSON><PERSON>duk (-/+ 1 hari) (DONE, WAIT QC)
   1. api & implement list, filter, update, export (DONE, WAIT QC)
   2. implement import by sku (DONE, WAIT QC)
2. Kiosk Registrasi & login, Produk (-/+ 1.5 hari)
   1. api & implement registrasi & login member
   2. api & implement list produk, filter
   3. implement cart
3. Kiosk Transaksi (-/+ 1.5 hari)
   1. setting pos
   2. api & implement checkout (cash, midtrans, handle poin (member & sales) )
   3. implement notifikasi
4. Admin Data Transaksi (-/+ 0.5 hari)
   1. api & implement list, filter, export
5. Laporan