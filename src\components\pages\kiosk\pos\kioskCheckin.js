/*
Created by esoda
Created on Feb, 2025
Contact esoda.id
*/

import React from "react";
import { Modal, Box } from "@mui/material";
import useTrackedCheckInStore from "@/store/kiosk/pos/modal/storeCheckIn";
import useTrackedPersistKioskPosStore, {
  usePersistKioskPosStore,
} from "@/store/kiosk/pos/storePersist";
import styles from "@/styles/Kiosk.module.css";
import Input from "@/components/libs/Input";
import CommonHelper from "@/utils/CommonHelper";

const KioskCheckIn = ({}) => {
  const {
    showDialog,
    isChecked,
    isVerified,
    isRegister,
    inputs,
    errors,
    onTextInputListeners,
    onCloseDialog,
    onCheckListeners,
    onVerifiedListeners,
    onNextListeners,
    setState,
    onSubmit,
    onSubmitRegister,
    onCancelRegister,
    onSendOTP,
  } = useTrackedCheckInStore();

  const { objCustomer } = useTrackedPersistKioskPosStore();

  const renderCustomerType = () => {
    return (
      <>
        {/* <div className={styles.kiosk_checkin}>
          <div
            className={inputs.customer_type_id === 2 && styles.active}
            onClick={() => {
              onTextInputListeners(2, "customer_type_id");
              onTextInputListeners("Umum", "customer_type_name");
            }}
          >
            Umum
          </div>
          <div
            className={inputs.customer_type_id === 1 && styles.active}
            onClick={() => {
              onTextInputListeners(1, "customer_type_id");
              onTextInputListeners("Member", "customer_type_name");
              //   setState checked verified
              setState({
                isChecked: false,
                isVerified: false,
              });

              setTimeout(() => {
                const inputWhatsapp = document.getElementById("input-whatsapp");
                if (inputWhatsapp) {
                  inputWhatsapp.focus();
                }
              }, 250);
            }}
          >
            Member
          </div>
        </div> */}
        <div className={styles.kiosk_checkin_input}>
          <div className={styles.input_search}>
            <label htmlFor="input-whatsapp">
              <i className="ph ph-whatsapp-logo"></i>
            </label>
            <input
              autocomplete="off"
              type="text"
              name="search"
              id="input-whatsapp"
              placeholder="Tuliskan nomor Whatsapp Anda..."
              value={inputs.customer_wa_number}
              onChange={(event) => {
                onTextInputListeners(event.target.value, "customer_wa_number");
              }}
            />
          </div>
          <a
            style={{ backgroundColor: "#3498db" }}
            onClick={() => {
              onCheckListeners();
            }}
          >
            Cek Data
          </a>
        </div>
        <p
          style={{
            fontSize: "14px",
            marginTop: ".5rem",
            textAlign: "justify",
          }}
        >
          Jika nomor WA Anda terdaftar sebagai member maka Anda akan menerima
          kode OTP untuk konfirmasi bahwa benar Anda yang melakukan transaksi.
          Terima kasih.
        </p>
        {isChecked && !isVerified && (
          <>
            <div className={`${styles.kiosk_checkin_input} mt-4`}>
              <div className={styles.input_search}>
                <label htmlFor="input-otp">
                  <i className="ph ph-numpad"></i>
                </label>
                <input
                  type="text"
                  name="search"
                  id="input-otp"
                  value={inputs.customer_otp}
                  onChange={(event) => {
                    onTextInputListeners(event.target.value, "customer_otp");
                  }}
                  placeholder="Tuliskan kode OTP..."
                />
              </div>
              <a
                style={{ backgroundColor: "#27ae60" }}
                onClick={() => {
                  onVerifiedListeners();
                }}
              >
                Konfirmasi
              </a>
              <a
                style={{ backgroundColor: "#e67e22" }}
                onClick={() => {
                  onSendOTP();
                }}
              >
                <i
                  className="ph ph-bold ph-arrow-counter-clockwise"
                  style={{ fontSize: "1.5rem" }}
                ></i>
              </a>
            </div>
          </>
        )}
      </>
    );
  };

  const renderCustomerResults = () => {
    return (
      <div
        className="pos-cashier-payment-results"
        style={{
          marginTop: "1rem",
          borderTop: "solid 1px rgb(237, 237, 237)",
        }}
      >
        <div className="icons">
          <i className="ph ph-bold ph-check"></i>
        </div>
        <h3>Member Terverifikasi!</h3>
        <h2
          style={{
            fontSize: "1rem",
            textAlign: "center",
          }}
        >
          Silahkan lanjutkan ke proses pembayaran untuk transaksi Anda
        </h2>
        <div className="infos">
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>Kode Member</div>
            <div style={{ fontSize: "1.1rem" }}>{objCustomer?.code}</div>
          </div>
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>Nama Member</div>
            <div style={{ fontSize: "1.1rem" }}>{objCustomer?.name}</div>
          </div>
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>Email</div>
            <div style={{ fontSize: "1.1rem" }}>
              {objCustomer?.email}
            </div>
          </div>
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>Nomor Whatsapp</div>
            <div style={{ fontSize: "1.1rem" }}>
              {objCustomer?.whatsapp}
            </div>
          </div>
          <div className="info">
            <div style={{ fontSize: "1.1rem" }}>Poin Tersedia</div>
            <div style={{ fontSize: "1.1rem" }}>{CommonHelper.formatNumber(objCustomer?.point)}</div>
          </div>
        </div>
      </div>
    );
  };

  let title = "Informasi Member";
  if (isChecked && !isVerified && !objCustomer?.id) {
    title = "Daftar Sebagai Member";
  }

  return (
    <>
      <Modal open={showDialog} onClose={onCloseDialog}>
        <Box
          sx={{
            flex: 1,
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            display: "flex",
            width: "100wh",
            height: "100vh",
          }}
        >
          <div className={`modal-content dialog sm`}>
            <div className="modal-header">
              <div className="title">{title}</div>
              <span className="close" onClick={onCloseDialog}>
                &times;
              </span>
            </div>
            <div className="modal-body">
              {!isRegister && renderCustomerType()}
              {isRegister && <RenderRegisterForm />}
              {!isRegister &&
                isChecked &&
                isVerified &&
                renderCustomerResults()}
            </div>
            {/* {inputs.customer_type_id === 1 && isChecked && isVerified && (
              <div className="modal-footer">
                <button
                  className="button"
                  onClick={() => {
                    onSubmit();
                  }}
                >
                  <i className="ph ph-bold ph-check-circle"></i>
                  <span>Lanjut Ke Pembayaran</span>
                </button>
                <button className="button cancel" onClick={onCloseDialog}>
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>Batal</span>
                </button>
              </div>
            )}
            {inputs.customer_type_id === 2 && (
              <div className="modal-footer">
                <button
                  className="button danger"
                  onClick={() => {
                    onSubmit();
                  }}
                >
                  <i className="ph ph-bold ph-check-circle"></i>
                  <span>Lanjut Ke Pembayaran</span>
                </button>
                <button className="button cancel" onClick={onCloseDialog}>
                  <i className="ph ph-bold ph-x-circle"></i>
                  <span>Batal</span>
                </button>
              </div>
            )} */}
            <div className="modal-footer">
              {isChecked && isVerified && (
                <button
                  className="button"
                  onClick={() => {
                    onSubmit();
                  }}
                >
                  <i className="ph ph-bold ph-check-circle"></i>
                  <span>Lanjut Ke Pembayaran</span>
                </button>
              )}
              {/* register */}
              {isRegister && isChecked && !isVerified && (
                <>
                  <button
                    className="button"
                    onClick={() => {
                      onSubmitRegister();
                    }}
                  >
                    <i className="ph ph-bold ph-check-circle"></i>
                    <span>Daftar Sebagai Member</span>
                  </button>
                  <button className="button cancel" onClick={onCancelRegister}>
                    <i className="ph ph-bold ph-x-circle"></i>
                    <span>Batal Daftar</span>
                  </button>
                </>
              )}
              {!isRegister && (
                <>
                  <button className="button cancel" onClick={onCloseDialog}>
                    <i className="ph ph-bold ph-x-circle"></i>
                    <span>Batal</span>
                  </button>
                </>
              )}
            </div>
          </div>
        </Box>
      </Modal>
    </>
  );
};

const RenderRegisterForm = () => {
  const {
    inputs,
    errors,
    onTextInputListeners,
    onTextErrorListeners,
    onSendOTP,
  } = useTrackedCheckInStore();

  return (
    <>
      <div className="flex-rows no-right">
        <div className="row no-border">
          <div className="input-form">
            <Input
              id="input-name"
              autocomplete="off"
              icon="ph ph-bold ph-user"
              label="Nama Lengkap"
              placeholder="Tuliskan nama lengkap Anda..."
              value={inputs.customer_name}
              onChange={(event) => {
                if (
                  event.target !== undefined &&
                  event.target.value !== undefined
                ) {
                  if (event.target.value.length <= 100) {
                    onTextInputListeners(event.target.value, "customer_name");
                  }
                }
              }}
              error={
                errors.customer_name !== undefined &&
                errors.customer_name !== null
                  ? errors.customer_name
                  : null
              }
              onFocus={() => onTextErrorListeners(null, "customer_name")}
              maxLength={100}
              required
            />
          </div>
          <div className="input-form">
            <Input
              autocomplete="off"
              icon="ph ph-bold ph-envelope-simple"
              label="Email"
              placeholder="Tuliskan email Anda..."
              value={inputs.customer_email_address}
              onChange={(event) => {
                if (
                  event.target !== undefined &&
                  event.target.value !== undefined
                ) {
                  if (event.target.value.length <= 100) {
                    onTextInputListeners(
                      event.target.value,
                      "customer_email_address"
                    );
                  }
                }
              }}
              error={
                errors.customer_email_address !== undefined &&
                errors.customer_email_address !== null
                  ? errors.customer_email_address
                  : null
              }
              onFocus={() =>
                onTextErrorListeners(null, "customer_email_address")
              }
              maxLength={100}
              optional
            />
          </div>
          <div className="input-form">
            <div className="flex flex-row gap-4 items-center">
              <Input
                autocomplete="off"
                icon="ph ph-bold ph-whatsapp-logo"
                label="Nomor Whatsapp"
                placeholder="Tuliskan nomor Whatsapp Anda..."
                value={inputs.customer_wa_number}
                onChange={(event) => {
                  if (
                    event.target !== undefined &&
                    event.target.value !== undefined
                  ) {
                    if (event.target.value.length <= 20) {
                      // remove non numeric, except +
                      onTextInputListeners(
                        event.target.value.replace(/[^0-9+]/g, ""),
                        "customer_wa_number"
                      );
                    }
                  }
                }}
                error={
                  errors.customer_wa_number !== undefined &&
                  errors.customer_wa_number !== null
                    ? errors.customer_wa_number
                    : null
                }
                onFocus={() => onTextErrorListeners(null, "customer_wa_number")}
                maxLength={20}
                required
              />
              {/* button kirim otp */}
              <button
                className="button info justify-center"
                onClick={() => {
                  onSendOTP();
                }}
                style={{
                  marginLeft: "auto",
                  width: "150px",
                  marginTop: ".3rem",
                }}
              >
                <span>Kirim OTP</span>
              </button>
            </div>
          </div>
          {/* otp */}
          <div className="input-form">
            <Input
              autocomplete="off"
              icon="ph ph-bold ph-numpad"
              label="Kode OTP"
              placeholder="Tuliskan kode OTP..."
              value={inputs.customer_otp}
              onChange={(event) => {
                if (
                  event.target !== undefined &&
                  event.target.value !== undefined
                ) {
                  if (event.target.value.length <= 6) {
                    // remove non numeric
                    onTextInputListeners(
                      event.target.value.replace(/[^0-9]/g, ""),
                      "customer_otp"
                    );
                  }
                }
              }}
              error={
                errors.customer_otp !== undefined &&
                errors.customer_otp !== null
                  ? errors.customer_otp
                  : null
              }
              onFocus={() => onTextErrorListeners(null, "customer_otp")}
              maxLength={6}
              required
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default KioskCheckIn;
