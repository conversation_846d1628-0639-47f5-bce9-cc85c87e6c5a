import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import { persist, createJSONStorage } from "zustand/middleware";
import CommonHelper from "@/utils/CommonHelper";
import Constants from "@/utils/Constants";
import { useKioskPosStore } from "./store";

const kioskPosCartStoreLogic = (set, get) => ({
  // State properties
  arrCart: [],
  refCartProductIds: {},
  totalProductSellingPrice: 0,

  objCustomer: null,

  // Actions
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }
      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  onAddToCart: (product) => {
    const currentArrCart = get().arrCart;
    let totalProductSellingPrice = get().totalProductSellingPrice;
    let refCartProductIds = get().refCartProductIds;
    let found = false;

    // It's better to map to a new array for immutability
    const newArrCart = currentArrCart.map((item) => {
      if (item.id === product.id) {
        found = true;
        totalProductSellingPrice += Number(item.selling_price);
        return { ...item, qty: item.qty + 1 };
      }
      return item;
    });

    if (!found) {
      totalProductSellingPrice += Number(product.selling_price);
      refCartProductIds[product.id] = true;
      newArrCart.push({ ...product, qty: 1 });
    }

    set({ arrCart: newArrCart, totalProductSellingPrice, refCartProductIds }); // Set the new array
  },

  // You might want other actions like removeFromCart, updateQuantity, clearCart
  onClearCart: () => {
    set({ arrCart: [], totalProductSellingPrice: 0, refCartProductIds: {} });
    useKioskPosStore.getState().onReset();
  },
});

// Create the persistent store
export const usePersistKioskPosStore = create(
  persist(
    kioskPosCartStoreLogic, // Your store logic
    {
      name: Constants.appName + "_KIOSK_POS", // Unique name for localStorage key
      storage: createJSONStorage(() => localStorage), // Or sessionStorage, AsyncStorage for RN
      partialize: (state) => ({
        arrCart: state.arrCart,
        refCartProductIds: state.refCartProductIds,
        totalProductSellingPrice: state.totalProductSellingPrice,
        objCustomer: state.objCustomer,
      }),
      // Optional: onRehydrateStorage can be useful for logging or migrations
      onRehydrateStorage: () => (state, error) => {
        if (error) {
          console.log(
            "Zustand persist: an error occurred during hydration",
            error
          );
        } else {
          console.log("Zustand persist: hydration finished", state);
        }
      },
    }
  )
);

// Your tracked selector hook remains the same
const useTrackedPersistKioskPosStore = createTrackedSelector(
  usePersistKioskPosStore
);

export default useTrackedPersistKioskPosStore;
