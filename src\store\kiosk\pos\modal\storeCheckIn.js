import { create } from "zustand";
import { createTrackedSelector } from "react-tracked";
import { useKioskPosStore } from "../store";
import { usePersistKioskPosStore } from "../storePersist";
import CommonHelper from "@/utils/CommonHelper";
import ApiHelper from "@/utils/ApiHelper";

import Router from "next/router";

const DEFAULT_INPUTS = {
  // customer_type_id: 1,
  // customer_type_name: "Member",
  // customer_id: 1,
  customer_name: "",
  // customer_image_url:
  //   "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQGXSeTomhrdIan6G5XRR_rk8zJ12BjlmXAG__1AnicoI7PC5HtZe9s26QERd2FqObM6sw&usqp=CAU",
  customer_email_address: "",
  customer_wa_number: "",
  customer_otp: "",
};

const state = (set, get) => ({
  setState: (newValue, cb = null, keys = []) => {
    set((prevState) => {
      if (keys.length > 0) {
        return CommonHelper.updateNestedState(prevState, keys, newValue);
      }

      // Shallow update if no keys provided
      return {
        ...prevState,
        ...newValue,
      };
    });

    if (cb) {
      setTimeout(() => {
        cb();
      }, 0);
    }
  },

  showDialog: false,
  formType: "checkin",
  formData: null,
  isChecked: false,
  isVerified: false,
  isRegister: false,
  inputs: structuredClone(DEFAULT_INPUTS),
  errors: {},

  onShowDialog: (formType = "checkin", formData = null) => {
    let objCustomer = usePersistKioskPosStore.getState().objCustomer;
    let isChecked = false;
    let isVerified = false;
    let isRegister = false;
    let inputs = structuredClone(DEFAULT_INPUTS);
    if (objCustomer?.id) {
      // isChecked = true;
      // isVerified = true;
      inputs = {
        ...inputs,
        // customer_wa_number: objCustomer.whatsapp,
      };
    }

    set({
      formType,
      formData,
      showDialog: true,
      isChecked,
      isVerified,
      isRegister,
      inputs,
    });

    if (get().inputs.customer_type_id === 1) {
      setTimeout(() => {
        const inputWhatsapp = document.getElementById("input-whatsapp");
        if (inputWhatsapp) {
          inputWhatsapp.focus();
        }
      }, 250);
    }
  },
  onCloseDialog: () => {
    set({
      showDialog: false,
      showDialog: false,
      formType: "checkin",
      formData: null,
      isChecked: false,
      isVerified: false,
      isRegister: false,

      inputs: structuredClone(DEFAULT_INPUTS),
      errors: {},
    });
  },
  onCheckListeners: async () => {
    let oldInputs = get().inputs;
    let inputs = structuredClone(DEFAULT_INPUTS);
    inputs.customer_wa_number = oldInputs.customer_wa_number;

    useKioskPosStore.getState().onLoading(true);
    set({ isChecked: false, isVerified: false, inputs });

    let params = {
      whatsapp: get().inputs.customer_wa_number,
      aol_id: useKioskPosStore.getState().selected_aol.aol_id,
      aol_session_database: useKioskPosStore.getState().selected_aol.database,
    };
    let response = await ApiHelper.get("kiosk/kiosk/member/check", params);

    let objCustomer = null;
    let isChecked = false;
    let isRegister = true;
    if (response.status === 200) {
      let data = response.results.data;
      if (data?.exist_bool) {
        objCustomer = data;
        isRegister = false;
        inputs.customer_name = data.name;
        inputs.customer_email_address = data.email;
      }
      isChecked = true;
    } else {
      useKioskPosStore
        .getState()
        .onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
    set({ isChecked, isRegister, inputs });
    usePersistKioskPosStore.getState().setState({ objCustomer });
    useKioskPosStore.getState().onLoading(false);
    if (!isRegister) {
      get().onSendOTP();
    } else {
      setTimeout(() => {
        const inputName = document.getElementById("input-name");
        if (inputName) {
          inputName.focus();
        }
      }, 250);
    }
  },
  onVerifiedListeners: async () => {
    useKioskPosStore.getState().onLoading(true);

    let params = {
      aol_id: useKioskPosStore.getState().selected_aol.aol_id,
      aol_session_database: useKioskPosStore.getState().selected_aol.database,
      whatsapp: get().inputs.customer_wa_number,
      otp: get().inputs.customer_otp,
    };
    let response = await ApiHelper.post("kiosk/kiosk/member/verify", params);

    if (response.status === 200) {
      useKioskPosStore
        .getState()
        .onNotify(response?.message || "Berhasil Verifikasi", "success");

      set({ isVerified: true });
      usePersistKioskPosStore.getState().setState({
        objCustomer: response.results.data,
      });
    } else {
      useKioskPosStore
        .getState()
        .onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
    useKioskPosStore.getState().onLoading(false);
  },
  onCancelRegister: () => {
    set({ isRegister: false, isChecked: false, isVerified: false });
  },
  onSubmitRegister: async () => {
    let isValid = true;
    if (!get().inputs.customer_name) {
      isValid = false;
      get().onTextErrorListeners(
        "Nama harus diisi atau tidak boleh kosong.",
        "customer_name"
      );
    }
    if (!get().inputs.customer_wa_number) {
      isValid = false;
      get().onTextErrorListeners(
        "Nomor Whatsapp harus diisi atau tidak boleh kosong.",
        "customer_wa_number"
      );
    }
    if (!get().inputs.customer_otp) {
      isValid = false;
      get().onTextErrorListeners(
        "Kode OTP harus diisi atau tidak boleh kosong.",
        "customer_otp"
      );
    }

    if (!isValid) {
      return;
    }

    useKioskPosStore.getState().onLoading(true);

    let params = {
      aol_id: useKioskPosStore.getState().selected_aol.aol_id,
      aol_session_database: useKioskPosStore.getState().selected_aol.database,
      branch_accurate_id:
        useKioskPosStore.getState().selected_branch.accurate_id,
      name: get().inputs.customer_name,
      email: get().inputs.customer_email_address,
      whatsapp: get().inputs.customer_wa_number,
      otp: get().inputs.customer_otp,
    };
    let response = await ApiHelper.post("kiosk/kiosk/member/register", params);

    if (response.status === 200) {
      useKioskPosStore
        .getState()
        .onNotify(response?.message || "Berhasil Daftar", "success");

      set({ isRegister: false, isChecked: true, isVerified: true });
      usePersistKioskPosStore.getState().setState({
        objCustomer: response.results.data,
      });
    } else {
      useKioskPosStore
        .getState()
        .onNotify(response?.message || "Terjadi Kesalahan", "error");
    }
    useKioskPosStore.getState().onLoading(false);
  },
  onSendOTP: async () => {
    let isValid = true;
    if (!get().inputs.customer_wa_number) {
      isValid = false;
      get().onTextErrorListeners(
        "Nomor Whatsapp harus diisi atau tidak boleh kosong.",
        "customer_wa_number"
      );
    } else if (get().inputs.customer_wa_number.length < 10) {
      isValid = false;
      get().onTextErrorListeners(
        "Nomor Whatsapp minimal 10 digit.",
        "customer_wa_number"
      );
    }
    if (!get().inputs.customer_name) {
      isValid = false;
      get().onTextErrorListeners(
        "Nama harus diisi atau tidak boleh kosong.",
        "customer_name"
      );
    }

    if (!isValid) {
      return;
    }

    useKioskPosStore.getState().onLoading(true);

    let params = {
      whatsapp: get().inputs.customer_wa_number,
      name: get().inputs.customer_name,
    };
    let response = await ApiHelper.post("kiosk/kiosk/member/otp/send", params);

    let otp = "";
    if (response.status === 200) {
      if (response.results.data?.otp) {
        otp = response.results.data.otp;
      }
      useKioskPosStore
        .getState()
        .onNotify(response?.message || "Berhasil Kirim OTP", "success");
    } else {
      useKioskPosStore
        .getState()
        .onNotify(response?.message || "Terjadi Kesalahan", "error");
    }

    set({ inputs: { ...get().inputs, customer_otp: otp } });
    useKioskPosStore.getState().onLoading(false);
  },

  onNextListeners: () => {
    // Simulate next step logic
    setTimeout(() => {
      get().onCloseDialog();
      if (get().onSubmit) {
        get().onSubmit(get().inputs);
      }
    }, 500);
  },

  onTextInputListeners: (text, input) => {
    set((state) => ({ inputs: { ...state.inputs, [input]: text } }));
  },
  onTextErrorListeners: (error, input) => {
    set((state) => ({ errors: { ...state.errors, [input]: error } }));
  },

  onSubmit: () => {
    // useKioskPosStore.getState().setState({ objCustomer });
    get().onCloseDialog();
    Router.push({ pathname: `/pos/payment` });
  },
});

export const useCheckInStore = create((...a) => ({
  ...state(...a),
}));

const useTrackedCheckInStore = createTrackedSelector(useCheckInStore);

export default useTrackedCheckInStore;
